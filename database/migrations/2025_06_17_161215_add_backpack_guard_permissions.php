<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::transaction(function () {
            // Get all existing permissions with 'web' guard
            $webPermissions = \Spatie\Permission\Models\Permission::where('guard_name', 'web')->get();

            foreach ($webPermissions as $webPermission) {
                // Create the same permission for 'backpack' guard if it doesn't exist
                \Spatie\Permission\Models\Permission::firstOrCreate([
                    'name' => $webPermission->name,
                    'guard_name' => 'backpack',
                ]);
            }

            // Get all roles with 'web' guard and create corresponding 'backpack' roles
            $webRoles = \Spatie\Permission\Models\Role::where('guard_name', 'web')->get();

            foreach ($webRoles as $webRole) {
                // Create the same role for 'backpack' guard if it doesn't exist
                $backpackRole = \Spatie\Permission\Models\Role::firstOrCreate([
                    'name' => $webRole->name,
                    'guard_name' => 'backpack',
                ]);

                // Get all permissions assigned to this web role
                $webPermissions = $webRole->permissions()->where('guard_name', 'web')->get();

                foreach ($webPermissions as $webPermission) {
                    // Find the corresponding backpack permission
                    $backpackPermission = \Spatie\Permission\Models\Permission::where('name', $webPermission->name)
                        ->where('guard_name', 'backpack')
                        ->first();

                    if ($backpackPermission) {
                        // Assign the backpack permission to the backpack role
                        $backpackRole->givePermissionTo($backpackPermission);
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove all permissions and roles with 'backpack' guard
        \Spatie\Permission\Models\Permission::where('guard_name', 'backpack')->delete();
        \Spatie\Permission\Models\Role::where('guard_name', 'backpack')->delete();
    }
};
