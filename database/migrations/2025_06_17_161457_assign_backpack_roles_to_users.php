<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Process users in chunks to avoid memory issues
        \App\Models\User\User::chunk(100, function ($users) {
            foreach ($users as $user) {
                // Get all web roles assigned to this user
                $webRoles = $user->roles()->where('guard_name', 'web')->get();

                foreach ($webRoles as $webRole) {
                    // Find the corresponding backpack role
                    $backpackRole = \Spatie\Permission\Models\Role::where('name', $webRole->name)
                        ->where('guard_name', 'backpack')
                        ->first();

                    if ($backpackRole) {
                        // Assign the backpack role to the user
                        $user->assignRole($backpackRole);
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Process users in chunks to avoid memory issues
        \App\Models\User\User::chunk(100, function ($users) {
            foreach ($users as $user) {
                $backpackRoles = $user->roles()->where('guard_name', 'backpack')->get();
                foreach ($backpackRoles as $role) {
                    $user->removeRole($role);
                }
            }
        });
    }
};
