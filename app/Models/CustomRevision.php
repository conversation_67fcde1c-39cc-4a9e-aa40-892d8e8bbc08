<?php

namespace App\Models;

use App\Models\User\User;
use Venturecraft\Revisionable\Revision as BaseRevision;

class CustomRevision extends BaseRevision
{
    protected $fillable = [
        'revisionable_type',
        'revisionable_id',
        'key',
        'old_value',
        'new_value',
        'user_id',
        'created_at',
        'updated_at'
    ];

    /**
     * User relationship for eager loading
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}