<?php

namespace App\Helpers\Revise;

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Venturecraft\Revisionable\Revision;
use Backpack\ReviseOperation\ReviseOperation;
use App\Helpers\Paginate;

trait ReviseOperationCustom
{
    use ReviseOperation;

    protected function setupReviseDefaults()
    {
        $this->crud->allowAccess('revise');

        $this->crud->operation('revise', function () {
            $this->crud->loadDefaultOperationSettingsFromConfig();
        });

        $this->crud->operation(['list', 'show'], function () {
            $this->crud->addButton('line', 'revise', 'view', 'vendor.backpack.revise-operation.resources.views.revise_button', 'end');
        });
        $this->crud->macro('getRevisionsForEntry', function ($id) {
            $revisions = [];
            foreach ($this->getEntry($id)->revisionHistory as $history) {
                $revisionDate = date('Y-m-d', strtotime((string)$history->created_at));
                if (!array_key_exists($revisionDate, $revisions)) {
                    $revisions[$revisionDate] = [];
                }
                array_unshift($revisions[$revisionDate], $history);
            }
            krsort($revisions);
            return $revisions;
        });

        $this->crud->macro('getPaginatedRevisionsForEntry', function ($id, $perPage = 20) {
            $entry = $this->getEntry($id);
            $currentPage = request()->get('page', 1);

            $paginatedRevisions = $entry->revisionHistory()
                ->select(['id', 'user_id', 'key', 'old_value', 'new_value', 'created_at'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $currentPage);

            $paginatedRevisions->withPath(request()->url());
            $paginatedRevisions->appends(request()->query());

            $groupedRevisions = [];
            foreach ($paginatedRevisions->items() as $history) {
                $revisionDate = date('Y-m-d', strtotime((string)$history->created_at));
                if (!array_key_exists($revisionDate, $groupedRevisions)) {
                    $groupedRevisions[$revisionDate] = [];
                }
                $groupedRevisions[$revisionDate][] = $history;
            }

            return [
                'revisions' => $groupedRevisions,
                'paginator' => $paginatedRevisions
            ];
        });
    }

    public function revise($id)
    {
        $this->crud->hasAccessOrFail('revise');
        $this->crud->setOperation('revise');

        $entry = $this->crud->getEntry($id);
        $perPage = config('backpack.operations.revise.per_page', 5);
        $paginatedData = $this->crud->getPaginatedRevisionsForEntry($id, $perPage);

        $data = [
            'crud' => $this->crud,
            'entry' => $entry,
            'revisions' => $paginatedData['revisions'],
            'paginator' => $paginatedData['paginator'],
            'title' => trans('revise-operation::revise.revisions'),
        ];

        if (request()->ajax()) {
            return view('revise-operation::revision_timeline_paginated', $data)->render();
        }

        return view('revise-operation::revisions_paginated', $data);
    }

    /**
     * Restore a specific revision
     */
    public function restore($entryId, $revisionId)
    {
        $this->crud->hasAccessOrFail('revise');
        $this->crud->setOperation('revise');

        try {
            $revision = Revision::findOrFail($revisionId);
            $entry = $this->crud->getEntry($entryId);

            if ($revision->revisionable_id != $entryId || $revision->revisionable_type != get_class($entry)) {
                return response()->json(['message' => 'Revisión no válida para esta entrada'], 400);
            }

            $fieldName = $revision->key;
            $oldValue = $revision->old_value;

            $entry->{$fieldName} = $oldValue;
            $entry->save();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Cambio restaurado exitosamente'
                ]);
            }

            return redirect()->back()->with('success', 'Cambio restaurado exitosamente');

        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error al restaurar: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error al restaurar: ' . $e->getMessage());
        }
    }
}
