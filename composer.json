{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-curl": "*", "ext-json": "*", "appstract/laravel-opcache": "^4.0", "backpack/backupmanager": "^4.0", "backpack/crud": "5.0.0", "backpack/filemanager": "^2.0.0", "backpack/permissionmanager": "^6.0", "backpack/pro": "^1.0.0", "backpack/revise-operation": "^1.0", "barryvdh/laravel-elfinder": "^0.5.0", "bugsnag/bugsnag-laravel": "^2.0", "doctrine/dbal": "^2.10.2", "fideloper/proxy": "^4.4", "guzzlehttp/guzzle": "^7.3", "jean85/pretty-package-versions": "^1.5 || ^2.0", "jenssegers/mongodb": "^3.6", "laravel/framework": "^9.0", "laravel/helpers": "^1.1", "laravel/socialite": "^5.5", "laravel/tinker": "^2.0", "laravel/ui": "^4.1.1", "league/flysystem-aws-s3-v3": "^3.10.3", "livewire/livewire": "^2.10", "maatwebsite/excel": "^3.1", "mongodb/mongodb": "^1.6", "predis/predis": "~1.0", "pusher/pusher-php-server": "^7.2", "sentry/sentry-laravel": "^2.5", "spatie/laravel-analytics": "^4.1", "spatie/laravel-backup": "^8.0", "tymon/jwt-auth": "^1.0", "intervention/image": "^2.7"}, "require-dev": {"backpack/generators": "^3.0", "barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laracasts/generators": "^2.0.1", "laravel/dusk": "^6.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://repo.backpackforlaravel.com/"}], "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}